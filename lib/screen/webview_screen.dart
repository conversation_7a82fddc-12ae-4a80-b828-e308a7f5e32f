// ignore_for_file: depend_on_referenced_packages, use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:karte_core/karte_core.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:kc_member_site_native/constant/enum.dart';
import 'package:kc_member_site_native/extensions/build_context_extensions.dart';
import 'package:kc_member_site_native/extensions/string_extensions.dart';
import 'package:kc_member_site_native/screen/base_state.dart';
import 'package:kc_member_site_native/service/index.dart';
import 'package:kc_member_site_native/util/app_review.dart';
import 'package:kc_member_site_native/util/cookie_manager.dart';
import 'package:kc_member_site_native/util/device_info_util.dart';
import 'package:kc_member_site_native/util/firebase_analytics.dart';
import 'package:kc_member_site_native/util/karte_util.dart';
import 'package:kc_member_site_native/view_model/webview_view_model.dart';
import 'package:move_to_background/move_to_background.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:webview_flutter/webview_flutter.dart';
// #docregion platform_imports
// Import for Android features.
import 'package:webview_flutter_android/webview_flutter_android.dart';
// Import for iOS features.
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

// #enddocregion platform_imports

import '../main.dart';
import '../routes/app_router.dart';
import '../util/app_dialog.dart';
import '../util/run_javascript.dart';

WebViewController? mainWebViewController;
String appUserAgent = Base.defaultUserAgent;
bool isLoading = true;
bool needReopen = false;

@RoutePage()
class WebViewScreen extends StatefulWidget implements AutoRouteWrapper {
  const WebViewScreen(this.initialUrl, {Key? key}) : super(key: key);
  final String initialUrl;

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return ChangeNotifierProvider(
      create: (ctx) => WebviewViewModel(
        context.read<LoginService>(),
      ),
      child: this,
    );
  }
}

class _WebViewScreenState extends BaseState<WebViewScreen, WebviewViewModel> with WidgetsBindingObserver {
  late final WebViewController _controller;

  /// iOS only
  double? _initSafeScreenHeight;

  /// iOS only
  double _prevViewInsetsBottom = 0;

  /// Android only
  bool _pageStarted = Platform.isIOS;

  String? _currentUrl;

  /// flag set true when start replace to other page
  bool _willUnmount = false;

  String? _userSyncScript;

  bool _shouldHybridComposition = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    UserSync.getUserSyncScript().then((value) {
      _userSyncScript = value;
    });
    // #docregion platform_features
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller = WebViewController.fromPlatformCreationParams(params);
    // #enddocregion platform_features

    controller
      ..setUserAgent(appUserAgent)
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.transparent)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            debugPrint('onProgress: ${progress.toString()}%');
            if (progress == 100 && _pageStarted) {
              if (!mounted) return;
              _pageStarted = Platform.isIOS;
              setState(() => isLoading = false);
              _onPageFinished();
            }
          },
          onPageStarted: (String url) {
            _pageStarted = true;
            isLoading = true;
            _currentUrl = url;
            debugPrint('onPageStarted: $url');
            if (!mounted) return;
            setState(() => isLoading = true);
            KarteUtil().setUserSyncScript(_userSyncScript);
          },
          onPageFinished: (String url) {
            debugPrint('onPageFinished: $url');
            // Warning: Sometime loadProgress = 100 but onPageFinished not fire
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('Page resource error:code: ${error.errorCode} description: ${error.description}');
            setState(() => isLoading = false);
          },
          onNavigationRequest: _onNavigationDelegate,
          onUrlChange: (UrlChange change) {
            debugPrint('url change to ${change.url}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'postAction',
        onMessageReceived: _onMessageReceived,
      )
      ..loadRequest(Uri.parse(widget.initialUrl));

    // #docregion platform_features
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(Base.currentEnv != Env.production);
      (controller.platform as AndroidWebViewController).setTextZoom(100);
    }
    if (controller.platform is WebKitWebViewController) {
      // (controller.platform as WebKitWebViewController).setInspectable(Base.currentEnv != Env.production);
      (controller.platform as WebKitWebViewController).setAllowsBackForwardNavigationGestures(true);
    }
    // #enddocregion platform_features

    _controller = controller;
    mainWebViewController ??= _controller;
    _shouldHybridComposition = DeviceInfoUtil().shouldHybridComposition();
  }

  Future<void> _onMessageReceived(JavaScriptMessage result) async {
    debugPrint('postAction: ${result.message}');
    final actionType = ActionType.from(result.message);
    switch (actionType) {
      case ActionType.home:
        if (Platform.isAndroid) MoveToBackground.moveTaskToBack();
        break;
      case ActionType.registerToken:
        await postDeviceToken(Platform.isAndroid ? fcmToken : apnsToken);
        break;
      case ActionType.deleteToken:
        await postDeleteDeviceToken(Platform.isAndroid ? fcmToken : apnsToken);
        break;
      case ActionType.updateSettings:
        _checkDeviceToken();
        break;
      case ActionType.updateSettingsFail:
        if (!mounted || _willUnmount) return;
        await showPushNotificationSettingError(
            context, [ActionProps(onPressed: (context) => Navigator.pop(context), child: Text(context.l10n.close))]);
        break;
      case ActionType.sessionInvalid:
        if (!mounted || _willUnmount) return;
        await showInvalidSession(context, [
          ActionProps(
              onPressed: (_) async {
                _replaceRoute(LoginRoute(actionType: ActionType.sessionInvalid));
              },
              child: Text(context.l10n.login))
        ]);
        break;
      case ActionType.logout:
        _replaceRoute(LoginRoute());
        break;
      case ActionType.unknown:
        final url = AppReview().parseMessage(result.message);
        AppReview().conditionsShowReview(
          context: context,
          url: url,
        );
        break;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      if (_shouldHybridComposition) {
        // HACK: fix android resume from background white screen, when touching any point on the webview, the content is displayed again
        try {
          // Force redraw DOM
          _controller.runJavaScript("var elmA14=document.createElement('div');document.body.appendChild(elmA14);").then((_) {
            _controller.runJavaScript("elmA14.remove()");
          });
        } catch (e) {
          debugPrint(e.toString());
        }
      }
      try {
        await _controller.runJavaScriptReturningResult("window.screen.height");
      } catch (e) {
        debugPrint("__AppLifecycleState.resumed runJavascriptReturningResult crash ${e.toString()}");
        _controller.reload();
      }
    }
    debugPrint("AppLifecycleState $state");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    //# --- start region fix keyboard issue ----
    final mq = MediaQuery.of(context);
    if (_prevViewInsetsBottom != mq.viewInsets.bottom) {
      try {
        if (Platform.isIOS) {
          // force webview to redraw: ref bug KCMSR-4095
          if (mq.viewInsets.bottom == 0) {
            _controller.runJavaScript("window.scrollBy(1, 1);window.scrollBy(-1, -1);");
          }
        } else {
          // Android keyboard hide input field https://guide.backlog.com/view/KCMSR-4146#comment-211398078
          EasyDebounce.debounce('scroll-debouncer', const Duration(milliseconds: 50), () {
            final mqy = MediaQuery.of(context);
            _controller.runJavaScript(
                "if(document.activeElement.getBoundingClientRect().top>${mqy.size.height - mqy.viewInsets.bottom - 140})document.activeElement.scrollIntoView({behavior:'smooth',block:'center',inline:'center'})");
          });
        }
      } catch (_) {}
    }
    _prevViewInsetsBottom = mq.viewInsets.bottom;
    if (_initSafeScreenHeight != null) return;
    _initSafeScreenHeight = mq.size.height - mq.padding.top - mq.padding.bottom;
    //# --- end region fix keyboard issue ----
  }

  @override
  void dispose() {
    mainWebViewController = null;
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (await _controller.canGoBack()) {
          _controller.goBack();
        } else if (Platform.isAndroid) {
          MoveToBackground.moveTaskToBack();
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: Platform.isAndroid,
        body: SafeArea(
            child: Stack(
          children: [
            Platform.isAndroid || _initSafeScreenHeight == null
                ? _buildWebView()
                : SizedBox(
                    height: _initSafeScreenHeight,
                    child: _buildWebView(),
                  ),
            Visibility(visible: isLoading, child: const LinearProgressIndicator()),
          ],
        )),
      ),
    );
  }

  Widget _buildWebView() {
    if (_willUnmount && Platform.isAndroid) return const SizedBox.shrink();

    /// fix conflict gesture with _SingleTouchRecognizer
    final gestureRecognizers = <Factory<OneSequenceGestureRecognizer>>{
      Factory<OneSequenceGestureRecognizer>(
        () => EagerGestureRecognizer(),
      ),
    };
    if (_controller.platform is AndroidWebViewController) {
      return WebViewWidget.fromPlatformCreationParams(
        params: AndroidWebViewWidgetCreationParams.fromPlatformWebViewWidgetCreationParams(
          AndroidWebViewWidgetCreationParams(
            controller: _controller.platform,
            gestureRecognizers: gestureRecognizers,
          ),
          displayWithHybridComposition: _shouldHybridComposition,
        ),
      );
    }
    return WebViewWidget(
      controller: _controller,
      gestureRecognizers: gestureRecognizers,
    );
  }

  NavigationDecision _onNavigationDelegate(NavigationRequest request) {
    final authUrl = request.url.checkAuthSecuritySettingUrl();
    if (authUrl != null) {
      vm.handleSecuritySettingsLogin(authUrl);
      return NavigationDecision.prevent;
    }
    if (request.url.startsWith(FirebaseAnalyticsUtils.webSchemeChannel)) {
      var params = _parseGetParamsFA(request);
      if (params.isNotEmpty) {
        FirebaseAnalyticsUtils.faTrackingFromWeb(params);
      }
      return NavigationDecision.prevent;
    }
    AppReview().conditionsShowReview(
      context: context,
      url: request.url,
    );
    if (!request.isMainFrame) return NavigationDecision.navigate;
    if (request.url.endsWith('logout-callback')) {
      _replaceRoute(LoginRoute());
      return NavigationDecision.prevent;
    } else if (request.url.isPdfUrl()) {
      context.router.push(PdfViewerRoute(pdfUrl: request.url));
      return NavigationDecision.prevent;
    } else if (request.url.shouldExternalBrowser()) {
      if (isLoading) {
        // Some case internal url redirect to external url so can't stop loading
        setState(() => isLoading = false);
      }
      launchUrlString(request.url, mode: LaunchMode.externalApplication);
      return NavigationDecision.prevent;
    } else {
      if (Platform.isAndroid && !isLoading) {
        // Start loading because Android onPageStarted does not run immediately but waits for the last request if url redirect multi time
        setState(() => isLoading = true);
      }
      return NavigationDecision.navigate;
    }
  }

  Map<String, String> _parseGetParamsFA(NavigationRequest request) {
    Map<String, String> params = {};
    var url = request.url.decodeFull();
    params[FirebaseAnalyticsUtils.eventNameKey] =
        url.substring('${FirebaseAnalyticsUtils.webSchemeChannel}://'.length, request.url.indexOf('?'));
    List<String> queries = url.split('?')[1].split('@@');
    for (var element in queries) {
      List<String> pair = element.split('\$\$');
      if (pair.length > 1) {
        params[pair[0]] = pair[1];
      }
    }
    return params;
  }

  void _onPageFinished() {
    KarteUtil().executeQueuePostNotifyKarteData();
    // Wait for runJavascript init
    Future.delayed(const Duration(milliseconds: 300), () async {
      KarteUtil().setUserSyncScript(_userSyncScript);

      if (queuePostDeviceToken.isNotEmpty) {
        queuePostDeviceToken.last();
        queuePostDeviceToken.clear();
      }
      if (queuePostNotifyData.isNotEmpty) {
        if (widget.initialUrl == Base.homePageUrl || _currentUrl?.startsWith(Base.homePageUrl) == true) {
          queuePostNotifyData.last();
        }
        queuePostNotifyData.clear();
      }
    });
  }

  void _checkDeviceToken() async {
    if (!await Permission.notification.isGranted) {
      if (!mounted || _willUnmount) return;
      await showPushNotificationGrant(context, [
        ActionProps(onPressed: (context) => Navigator.pop(context), child: Text(context.l10n.close)),
        ActionProps(
            onPressed: (context) async {
              await openAppSettings();
              if (!mounted) return;
              Navigator.pop(context);
            },
            child: Text(context.l10n.setting)),
      ]);
    } else {
      await postPushNotifySettings(Platform.isAndroid ? fcmToken : apnsToken);
    }
  }

  void _replaceRoute(PageRouteInfo<dynamic> route) async {
    _willUnmount = true;
    if (Platform.isAndroid) {
      setState(() {});
    }
    await clearSession();
    context.router.replace(route);
  }
}
