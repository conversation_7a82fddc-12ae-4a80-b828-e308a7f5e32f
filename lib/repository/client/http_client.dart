import 'dart:io';

import 'package:flutter/material.dart';
import 'package:kc_member_site_native/constant/base.dart';
import 'package:native_flutter_proxy/native_proxy_reader.dart';

Future<Map<String, dynamic>?> _getProxySetting() async {
  bool enabled = false;
  String? host;
  int? port;
  try {
    ProxySetting settings = await NativeProxyReader.proxySetting;
    enabled = settings.enabled;
    host = settings.host;
    port = settings.port;
  } catch (e) {
    debugPrint(e.toString());
  }
  if (enabled && host != null) {
    return {"host": host, "port": port};
  }
  return null;
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final httpClient = super.createHttpClient(context);
    httpClient.connectionTimeout = const Duration(seconds: Base.networkTimeout);

    // if (Base.currentEnv != Env.production) {
    httpClient.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    _getProxySetting().then((proxy) {
      if (proxy == null) return;
      httpClient.findProxy = (uri) {
        return "PROXY ${proxy["host"]}:${proxy["port"]};";
      };
    });
    // }
    return httpClient;
  }
}
