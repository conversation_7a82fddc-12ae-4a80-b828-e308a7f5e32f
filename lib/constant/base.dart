import 'dart:io';

import 'package:kc_member_site_native/constant/enum.dart';

enum Env {
  /// ITa
  develop,

  st,

  /// ITb
  staging,

  adt,

  /// Honban
  production,
}

class Base {
  static final instance = Base._internal();
  Base._internal();
  factory Base() {
    return instance;
  }

  /// 45s
  static const networkTimeout = 45;
  static const timeoutExceptionMsg = "Operation timed out";

  /// 3s
  static const getGeoTimeout = 3;

  static const String _flavor = String.fromEnvironment('FLAVOR');

  /// A | F | G | H | SPETB
  static const String _domainType = String.fromEnvironment('DOMAIN_TYPE');

  static const geoIP2CheckToken = 'GEO_IP2_TOKEN';

  static String get platformName => Platform.isAndroid ? 'Android' : 'iOS';

  static Env currentEnv = Env.values.firstWhere((e) => e.name == _flavor, orElse: () => Env.develop);

  static const _kcMemberSite0StgUrl = {
    StgType.A: 'https://s20-si0-e0.dev2.devcabu.jp',
    StgType.F: 'https://s20-si0-f.dev2.devcabu.jp',
    StgType.G: 'https://s20-si0-g.dev2.devcabu.jp',
    StgType.H: 'https://s20-si0-h.dev2.devcabu.jp',
    StgType.nNISA: 'https://s20-si0-nnisa.dev2.devcabu.jp',
  };
  static const _kcMemberSite1StgUrl = {
    StgType.A: 'https://s20-si1-e0.dev2.devcabu.jp',
    StgType.F: 'https://s20-si1-f.dev2.devcabu.jp',
    StgType.G: 'https://s20-si1-g.dev2.devcabu.jp',
    StgType.H: 'https://s20-si1-h.dev2.devcabu.jp',
    StgType.nNISA: 'https://s20-si1-nnisa.dev2.devcabu.jp',
  };

  static const _kcMemberSite0StUrl = {
    StType.a: 'https://s20-si0-a.devst.devcabu.jp',
    StType.dua2a: 'https://s20-si0-dua2a.devst.devcabu.jp',
  };
  static const _kcMemberSite1StUrl = {
    StType.a: 'https://s20-si1-a.devst.devcabu.jp',
    StType.dua2a: 'https://s20-si1-dua2a.devst.devcabu.jp',
  };

  static String get bffDomain {
    switch (currentEnv) {
      case Env.production:
        return 'd10-spsite.kabu.co.jp';
      case Env.adt:
        return AdtType.from(_domainType).domain;
      case Env.staging:
        return StgType.from(_domainType).domain;
      case Env.st:
        return StType.from(_domainType).domain;
      default:
        return DevType.from(_domainType).domain;
    }
  }

  static String get announceDomainUrl {
    switch (currentEnv) {
      case Env.production:
        return 'https://announce-information.kabu.co.jp';
      case Env.staging:
      case Env.st:
      case Env.adt:
        return 'https://announce-information-a.dev2.devcabu.jp';
      default:
        return 'https://auth0-stub.kcmsr.dev.guide.inc';
    }
  }

  static String get loginStatusDomainUrl {
    switch (currentEnv) {
      case Env.production:
        return 'https://login-status.kabu.co.jp';
      case Env.staging:
      case Env.st:
      case Env.adt:
        return 'https://login-status-a.dev2.devcabu.jp';
      default:
        return 'https://auth0-stub.kcmsr.dev.guide.inc';
    }
  }

  static String get bffDomainUrl => 'https://$bffDomain';

  static String get mfaDomainUrl => bffDomainUrl;

  static String get banhmiDomainUrl {
    switch (currentEnv) {
      case Env.production:
        return 'https://banhmi-api.trigger.guide.inc/prod';
      case Env.develop:
        return 'https://banhmi-api.trigger.guide.inc/dev'; // For skip check GEO location
      default:
        return "https://banhmi-api.trigger.stg.guide.inc/stg";
    }
  }

  static String get kcMemberSite0Url {
    switch (currentEnv) {
      case Env.production:
        return 'https://s20.si0.kabu.co.jp';
      case Env.staging:
        return _kcMemberSite0StgUrl[StgType.from(_domainType)] ?? 'https://s20-si0-e0.dev2.devcabu.jp';
      case Env.st:
        return _kcMemberSite0StUrl[StType.from(_domainType)] ?? 'https://s20-si0-e0.dev2.devcabu.jp';
      default:
        return 'https://s20-si0-e0.dev2.devcabu.jp';
    }
  }

  static String get kcMemberSite1Url {
    switch (currentEnv) {
      case Env.production:
        return 'https://s20.si1.kabu.co.jp';
      case Env.staging:
        return _kcMemberSite1StgUrl[StgType.from(_domainType)] ?? 'https://s20-si1-e0.dev2.devcabu.jp';
      case Env.st:
        return _kcMemberSite1StUrl[StType.from(_domainType)] ?? 'https://s20-si1-e0.dev2.devcabu.jp';
      default:
        return 'https://s20-si1-e0.dev2.devcabu.jp';
    }
  }

  static String _kcMemberSiteUrl = "";
  static String get kcMemberSiteUrl => _kcMemberSiteUrl;

  static set setKcMemberSiteUrl(String url) {
    _kcMemberSiteUrl = url;
  }

  static String get configurationFileUrl {
    switch (currentEnv) {
      case Env.production:
        return 'https://spimage.kabu.co.jp';
      case Env.staging:
        return 'https://spimage.dev2.devcabu.jp';
      case Env.st:
        return 'https://spimage.devst.devcabu.jp';
      case Env.adt:
        return 'https://spimage.devadt.devcabu.jp';
      default:
        return 'https://universal-link.kcmsr.dev.guide.inc';
    }
  }

  static String defaultUserAgent = '/kabu.com kcmsr for ${Platform.isAndroid ? 'Android' : 'iPhone'} / ';

  // Url
  static String homePageUrl = '$bffDomainUrl/mobile/mypage/portfolio';
  static String securitySettingTopUrl = '$bffDomainUrl/mobile/setting/security/top';

  static String howToChangeAuthEmailUrl = 'https://faq.kabu.com/s/article/k007478';
  static String whatHappenIfNoAuthUrl = 'https://faq.kabu.com/s/article/k007479';
  static String recoveryCodeIssueMethodUrl = 'https://faq.kabu.com/s/article/k003121';
  static String get recoveryCodeResetUrl {
    switch (currentEnv) {
      case Env.production:
        return 'https://preauth.kabu.co.jp/account-setting/recovery-code-reset';
      case Env.staging:
      case Env.st:
      case Env.adt:
        return 'https://preauth-dua2b.devst.devcabu.jp/account-setting/recovery-code-reset';
      default:
        return 'https://external.st.a-ita.kcmsr.dev.guide.inc/account-setting/recovery-code-reset';
    }
  }

  static String needsTradingInfoEntryUrl =
      '$kcMemberSiteUrl/iphone/FirstLoginInput/FLI01101.asp?/iphone/kcmsr_login.asp?returnUrl=$bffDomainUrl/mobile';
  static String needsFxAccountOpeningUrl = '$kcMemberSiteUrl/ap/iPhone/Personal/WebExaminationFX/ExaminationInput';
  static String loginFailUrl = '$bffDomainUrl/mobile/login-fail';
  static String preContractConfirmUrl = '$bffDomainUrl/mobile/setting/electronic-delivery/pre-contract-documents/notice';
  static String mailRegistrationGuidanceUrl = '$bffDomainUrl/mobile/setting/mail-registration/guidance';
  static const String troubleLoggingInUrl = 'https://faq.kabu.com/s/topic/0TO7F000000TqXmWAK';
  static String get createAccountUrl {
    switch (currentEnv) {
      case Env.production:
        return 'https://acs.kabu.co.jp/sp/input/gw';
      default:
        return 'https://acs.dev2.devcabu.jp/sp/input/gw';
    }
  }

  static final authSecurityLoginUrl = "$bffDomainUrl/auth/security-setting/login";
  static final authSecurityMfaLoginUrl = "$bffDomainUrl/auth/security-setting/mfa-login";

  static final authNativeSecurityLoginPaths = {
    authSecurityLoginUrl: "/auth/native-security-setting/login",
    authSecurityMfaLoginUrl: "/auth/native-security-setting/mfa-login",
  };
  static const spWebLoginUrl = "https://d10-spsite.kabu.co.jp/auth/sp-web/login";

  static const loginCompleteSuffix = "/login-complete.html";
  static const mfaLoginCompleteSuffix = "/mfa-login-complete.html";
  static const mfaLoginCompletePath = "/mfa-login-complete";

  static const customUrlScheme = "kabuappnext://security-setting";

  // Store Url
  static const iOSStoreUrl = "https://itunes.apple.com/jp/app/id1661251290";
  static const androidStoreUrl = "https://play.google.com/store/apps/details?id=com.kabu.kabuappNext";

  // Route path
  static const loginPath = '/native/login';
  static const licensesPath = '/native/licenses';
  static const pdfViewerPath = '/native/pdfViewer';
  static const webviewPath = '/native/webview';

  static const mapScreenClass = {
    loginPath: "ログイン",
    licensesPath: "ライセンス",
    pdfViewerPath: "PDFViewer",
  };
}
