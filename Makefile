.PHONY: clean gen gen-l10n gen-app-icon apk-analyze-size build-apk build-apk-staging build-apk-adt build-appbundle build-ipa build-ipa-st build-ipa-dev-store build-ipa-stg-store build-ipa-adt apply-env backup-config restore-config

clean:
	fvm flutter clean
	fvm flutter pub get
	fvm flutter gen-l10n
	fvm dart run build_runner build --delete-conflicting-outputs

gen:
	fvm flutter gen-l10n
	fvm dart run build_runner build --delete-conflicting-outputs

gen-l10n:
	fvm flutter gen-l10n

gen-app-icon:
	fvm dart run flutter_launcher_icons

apk-analyze-size:
	fvm flutter build apk --release --flavor develop --dart-define=FLAVOR=develop --analyze-size --target-platform android-arm64


# Build Android
build-apk:
	fvm flutter build apk --release --flavor develop --dart-define=FLAVOR=develop --obfuscate --split-debug-info=build/obfuscate
build-apk-staging:
	fvm flutter build apk --release --flavor staging --dart-define=FLAVOR=staging --dart-define=DOMAIN_TYPE=A --obfuscate --split-debug-info=build/obfuscate
build-apk-adt:
	fvm flutter build apk --release --flavor adt --dart-define=FLAVOR=adt --dart-define=DOMAIN_TYPE=A --obfuscate --split-debug-info=build/obfuscate
build-appbundle:
	fvm flutter build appbundle --release --flavor develop --dart-define=FLAVOR=develop --obfuscate --split-debug-info=build/obfuscate

# Build iOS
build-ipa:
	fvm flutter build ipa --release --flavor develop --dart-define=FLAVOR=develop --obfuscate --split-debug-info=build/obfuscate --export-options-plist=ios/options/ExportOptionsDev.plist
build-ipa-st:
	fvm flutter build ipa --release --flavor st --dart-define=FLAVOR=st --obfuscate --split-debug-info=build/obfuscate --export-options-plist=ios/options/ExportOptionsSt.plist
build-ipa-dev-store:
	fvm flutter build ipa --release --flavor developStore --dart-define=FLAVOR=develop --obfuscate --split-debug-info=build/obfuscate --export-options-plist=ios/options/ExportOptionsDevStore.plist
build-ipa-stg-store:
	fvm flutter build ipa --release --flavor stagingStore --dart-define=FLAVOR=staging --obfuscate --split-debug-info=build/obfuscate --export-options-plist=ios/options/ExportOptionsStgStore.plist
build-ipa-adt:
	fvm flutter build ipa --release --flavor adt --dart-define=FLAVOR=adt --dart-define=DOMAIN_TYPE=A --obfuscate --split-debug-info=build/obfuscate --export-options-plist=ios/options/ExportOptionsAdt.plist

# Apply Environment Variables
apply-env:
	@if [ -z "$(ENV)" ]; then echo "Usage: make apply-env ENV=<environment>"; echo "Available: dev, prod, stg, adt, st"; exit 1; fi
	@./scripts/apply_env.sh $(ENV)

# Configuration Management
backup-config:
	@./scripts/config_manager.sh backup

restore-config:
	@./scripts/config_manager.sh restore
